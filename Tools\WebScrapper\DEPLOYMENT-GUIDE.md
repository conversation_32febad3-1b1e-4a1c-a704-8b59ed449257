# 🚀 WebScrapper Lambda Deployment Guide

## ✅ Important Settings Configuration

Your Lambda function requires these specific settings for web scraping to work properly:

### 1. 🧠 **Memory Configuration: 2048 MB** ✅ ALREADY CONFIGURED
- **Why**: Chrome browser requires significant memory to run
- **Status**: ✅ Already set in both `template.yaml` and `serverless.yml`

### 2. ⏱️ **Timeout: 300 seconds (5 minutes)** ✅ ALREADY CONFIGURED  
- **Why**: Web scraping can take time, especially for slow-loading websites
- **Status**: ✅ Already set in both deployment configurations

### 3. 🌐 **Chrome Layer** ✅ NOW CONFIGURED

#### **Option A: Public Chrome Layer (Recommended - Already Configured)**
Your deployment files now use a public Chrome layer that works across regions:

**SAM Template (`template.yaml`):**
```yaml
Layers:
  - arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31
```

**Serverless (`serverless.yml`):**
```yaml
layers:
  - arn:aws:lambda:${aws:region}:************:layer:chrome-aws-lambda:31
```

#### **Chrome Layer ARNs by Region:**
- **US East 1 (N. Virginia)**: `arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31`
- **US West 2 (Oregon)**: `arn:aws:lambda:us-west-2:************:layer:chrome-aws-lambda:31`
- **EU West 1 (Ireland)**: `arn:aws:lambda:eu-west-1:************:layer:chrome-aws-lambda:31`
- **EU Central 1 (Frankfurt)**: `arn:aws:lambda:eu-central-1:************:layer:chrome-aws-lambda:31`
- **AP Southeast 1 (Singapore)**: `arn:aws:lambda:ap-southeast-1:************:layer:chrome-aws-lambda:31`

## 🚀 **Deployment Steps**

### **Method 1: AWS SAM (Recommended)**

1. **Install AWS SAM CLI** (if not already installed):
   ```bash
   # Windows (using Chocolatey)
   choco install aws-sam-cli
   
   # Or download from: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html
   ```

2. **Configure AWS credentials**:
   ```bash
   aws configure
   ```

3. **Deploy using the script**:
   ```powershell
   .\deploy.ps1
   ```

4. **Or deploy manually**:
   ```bash
   sam build
   sam deploy --guided
   ```

### **Method 2: Serverless Framework**

1. **Install Serverless Framework**:
   ```bash
   npm install -g serverless
   ```

2. **Deploy**:
   ```bash
   serverless deploy
   ```

## 🔧 **Manual Configuration (If Needed)**

If you need to modify settings after deployment:

### **Via AWS Console:**

1. **Go to AWS Lambda Console**
2. **Find your WebScrapper function**
3. **Configuration Tab → General Configuration:**
   - Memory: 2048 MB
   - Timeout: 5 minutes (300 seconds)

4. **Configuration Tab → Layers:**
   - Add layer: `arn:aws:lambda:YOUR_REGION:************:layer:chrome-aws-lambda:31`

### **Via AWS CLI:**

```bash
# Update memory and timeout
aws lambda update-function-configuration \
  --function-name WebScrapper \
  --memory-size 2048 \
  --timeout 300

# Add Chrome layer
aws lambda update-function-configuration \
  --function-name WebScrapper \
  --layers arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31
```

## 🧪 **Testing After Deployment**

1. **Test the health endpoint**:
   ```bash
   curl https://YOUR_API_GATEWAY_URL/Test
   ```

2. **Test the web scraping endpoint**:
   ```bash
   curl "https://YOUR_API_GATEWAY_URL/PersonDetails?url=https://zh.chregister.ch/cr-portal/auszug/auszug.xhtml?uid=CHE-442.398.437" \
     -H "Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg=="
   ```

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **"Chrome not found" error**:
   - Verify the Chrome layer is attached
   - Check the layer ARN matches your region

2. **Memory errors**:
   - Increase memory to 3008 MB if needed
   - Monitor CloudWatch metrics

3. **Timeout errors**:
   - Increase timeout if scraping very slow sites
   - Check target website availability

4. **Authentication errors**:
   - Verify Basic Auth header format
   - Check credentials in environment variables

### **Monitoring:**
- **CloudWatch Logs**: `/aws/lambda/WebScrapper`
- **CloudWatch Metrics**: Lambda function metrics
- **X-Ray Tracing**: Enable for detailed performance analysis

## 🎉 **You're All Set!**

Your WebScrapper Lambda function is now properly configured with:
- ✅ Sufficient memory (2048 MB)
- ✅ Adequate timeout (300 seconds)  
- ✅ Chrome layer for web scraping
- ✅ Proper API Gateway integration
- ✅ CORS configuration
- ✅ Basic authentication

Deploy and enjoy your serverless web scraping service! 🚀
