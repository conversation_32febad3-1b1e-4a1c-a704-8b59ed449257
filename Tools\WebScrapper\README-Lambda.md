# WebScrapper AWS Lambda Function

This project has been converted from an ASP.NET Core Web API to an AWS Lambda function for better scalability and cost-effectiveness.

## Overview

The WebScrapper Lambda function extracts person details from Swiss company register websites. It uses Selenium WebDriver with Chrome in headless mode to scrape dynamic content.

## Architecture

- **Runtime**: .NET 8
- **Handler**: `WebScrapper::WebScrapper.Function::FunctionHandler`
- **Memory**: 2048 MB (recommended for Chrome)
- **Timeout**: 300 seconds (5 minutes)
- **Architecture**: x86_64

## Prerequisites

1. **AWS CLI** - [Install AWS CLI](https://aws.amazon.com/cli/)
2. **.NET 8 SDK** - [Download .NET 8](https://dotnet.microsoft.com/download/dotnet/8.0)
3. **AWS SAM CLI** (recommended) - [Install SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html)
   - OR **Serverless Framework** - `npm install -g serverless`

## Chrome Layer Requirement

⚠️ **Important**: This Lambda function requires Chrome to be available. You have several options:

### Option 1: Use a Public Chrome Layer
```bash
# Add this layer ARN to your Lambda function:
arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31
```

### Option 2: Create Your Own Chrome Layer
1. Download Chrome for Lambda from [chrome-aws-lambda](https://github.com/alixaxel/chrome-aws-lambda)
2. Create a layer with the Chrome binary
3. Update the layer ARN in `template.yaml` or `serverless.yml`

## Deployment Options

### Option 1: AWS SAM (Recommended)

1. **Configure AWS credentials**:
   ```bash
   aws configure
   ```

2. **Deploy using the script**:
   ```bash
   # Windows
   .\deploy.ps1
   
   # Linux/Mac
   ./deploy.sh
   ```

3. **Manual SAM deployment**:
   ```bash
   sam build
   sam deploy --guided
   ```

### Option 2: Serverless Framework

1. **Install Serverless Framework**:
   ```bash
   npm install -g serverless
   ```

2. **Deploy**:
   ```bash
   serverless deploy
   ```

## Environment Variables

Set these environment variables for authentication:

- `BASIC_AUTH_USERNAME` (default: "alanadmin")
- `BASIC_AUTH_PASSWORD` (default: "alan!@21ALAN")

## API Endpoints

### GET /Test
Health check endpoint to verify the Lambda function is running.

**Example**:
```bash
curl -X GET 'https://your-api-gateway-url/Test'
```

**Response**:
```json
{
  "message": "API service is running"
}
```

### GET /PersonDetails
Extracts person details from a Swiss company register URL.

**Parameters**:
- `url` (required): Swiss company register URL

**Authentication**: Basic Auth required

**Example**:
```bash
curl -X GET 'https://your-api-gateway-url/PersonDetails?url=https://zh.chregister.ch/cr-portal/auszug/auszug.xhtml?uid=CHE-442.398.437' \
  -H 'Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg=='
```

**Response**:
```json
[
  {
    "personalDetails": "John Doe",
    "role": "Manager",
    "signingAuthority": "Individual signing authority"
  }
]
```

## Local Development

### Testing Locally with SAM

1. **Start local API**:
   ```bash
   sam local start-api
   ```

2. **Test endpoints**:
   ```bash
   curl http://localhost:3000/Test
   ```

### Testing with .NET Lambda Test Tool

1. **Install the tool**:
   ```bash
   dotnet tool install -g Amazon.Lambda.TestTool-8.0
   ```

2. **Run locally**:
   ```bash
   cd WebScrapper
   dotnet lambda-test-tool-8.0
   ```

## Configuration

### Lambda Function Configuration

- **Memory**: 2048 MB (Chrome requires significant memory)
- **Timeout**: 300 seconds (web scraping can be slow)
- **Environment Variables**:
  - `ASPNETCORE_ENVIRONMENT=Production`
  - `BASIC_AUTH_USERNAME=your_username`
  - `BASIC_AUTH_PASSWORD=your_password`

### Chrome Configuration

The function automatically detects if it's running in Lambda and applies appropriate Chrome options:

- Headless mode
- No sandbox (required for Lambda)
- Disabled dev-shm-usage
- Memory optimizations
- Single process mode (Lambda-specific)

## Monitoring and Logging

- **CloudWatch Logs**: All logs are automatically sent to CloudWatch
- **Metrics**: Lambda metrics available in CloudWatch
- **X-Ray Tracing**: Can be enabled for detailed tracing

## Troubleshooting

### Common Issues

1. **Chrome not found**:
   - Ensure Chrome layer is properly attached
   - Check layer ARN is correct for your region

2. **Memory issues**:
   - Increase Lambda memory to 2048 MB or higher
   - Chrome requires significant memory

3. **Timeout issues**:
   - Increase timeout to 300 seconds
   - Some websites load slowly

4. **Authentication failures**:
   - Verify Basic Auth credentials
   - Check Authorization header format

### Debug Mode

To enable debug HTML saving, the function saves scraped HTML to `/tmp/debug_page.html` in Lambda.

## Cost Optimization

- Lambda charges only for execution time
- Consider using provisioned concurrency for consistent performance
- Monitor CloudWatch metrics to optimize memory allocation

## Security Considerations

- Basic authentication credentials should be stored in AWS Secrets Manager
- Consider using API Gateway authorizers for enhanced security
- Enable AWS WAF for additional protection

## Migration from ASP.NET Core

Key changes made during migration:

1. Replaced ASP.NET Core controllers with Lambda function handler
2. Updated Chrome configuration for Lambda environment
3. Modified logging to use CloudWatch
4. Removed web-specific dependencies
5. Added Lambda-specific deployment configurations

## Support

For issues related to:
- **AWS Lambda**: Check CloudWatch logs
- **Chrome/Selenium**: Verify Chrome layer configuration
- **Web Scraping**: Check target website structure changes
