using Amazon.Lambda.APIGatewayEvents;
using Amazon.Lambda.TestUtilities;
using System.Text.Json;
using Xunit;

namespace WebScrapper.Tests;

public class FunctionTests
{
    [Fact]
    public async Task TestFunction_TestEndpoint_ReturnsSuccess()
    {
        // Arrange
        var function = new Function();
        var context = new TestLambdaContext();
        var request = new APIGatewayProxyRequest
        {
            HttpMethod = "GET",
            Path = "/Test",
            Headers = new Dictionary<string, string>(),
            QueryStringParameters = new Dictionary<string, string>()
        };

        // Act
        var response = await function.FunctionHandler(request, context);

        // Assert
        Assert.Equal(200, response.StatusCode);
        Assert.Contains("API service is running", response.Body);
    }

    [Fact]
    public async Task TestFunction_PersonDetailsWithoutAuth_ReturnsUnauthorized()
    {
        // Arrange
        var function = new Function();
        var context = new TestLambdaContext();
        var request = new APIGatewayProxyRequest
        {
            HttpMethod = "GET",
            Path = "/PersonDetails",
            Headers = new Dictionary<string, string>(),
            QueryStringParameters = new Dictionary<string, string>
            {
                { "url", "https://zh.chregister.ch/cr-portal/auszug/auszug.xhtml?uid=CHE-442.398.437" }
            }
        };

        // Act
        var response = await function.FunctionHandler(request, context);

        // Assert
        Assert.Equal(401, response.StatusCode);
        Assert.Contains("Unauthorized", response.Body);
    }

    [Fact]
    public async Task TestFunction_PersonDetailsWithAuth_RequiresUrl()
    {
        // Arrange
        var function = new Function();
        var context = new TestLambdaContext();
        var request = new APIGatewayProxyRequest
        {
            HttpMethod = "GET",
            Path = "/PersonDetails",
            Headers = new Dictionary<string, string>
            {
                { "Authorization", "Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==" } // alanadmin:alan!@21ALAN
            },
            QueryStringParameters = new Dictionary<string, string>()
        };

        // Act
        var response = await function.FunctionHandler(request, context);

        // Assert
        Assert.Equal(400, response.StatusCode);
        Assert.Contains("URL parameter is required", response.Body);
    }

    [Fact]
    public async Task TestFunction_InvalidPath_ReturnsNotFound()
    {
        // Arrange
        var function = new Function();
        var context = new TestLambdaContext();
        var request = new APIGatewayProxyRequest
        {
            HttpMethod = "GET",
            Path = "/InvalidPath",
            Headers = new Dictionary<string, string>(),
            QueryStringParameters = new Dictionary<string, string>()
        };

        // Act
        var response = await function.FunctionHandler(request, context);

        // Assert
        Assert.Equal(404, response.StatusCode);
        Assert.Contains("Not Found", response.Body);
    }

    [Fact]
    public void TestBasicAuthEncoding()
    {
        // Test that our expected Basic Auth header is correct
        var credentials = "alanadmin:alan!@21ALAN";
        var encoded = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(credentials));
        
        Assert.Equal("YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==", encoded);
    }

    [Fact]
    public async Task TestFunction_PersonDetailsWithValidAuth_HandlesEmptyUrl()
    {
        // Arrange
        var function = new Function();
        var context = new TestLambdaContext();
        var request = new APIGatewayProxyRequest
        {
            HttpMethod = "GET",
            Path = "/PersonDetails",
            Headers = new Dictionary<string, string>
            {
                { "Authorization", "Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg==" }
            },
            QueryStringParameters = new Dictionary<string, string>
            {
                { "url", "" }
            }
        };

        // Act
        var response = await function.FunctionHandler(request, context);

        // Assert
        Assert.Equal(400, response.StatusCode);
        Assert.Contains("URL parameter is required", response.Body);
    }

    [Fact]
    public async Task TestFunction_ResponseHasCorsHeaders()
    {
        // Arrange
        var function = new Function();
        var context = new TestLambdaContext();
        var request = new APIGatewayProxyRequest
        {
            HttpMethod = "GET",
            Path = "/Test",
            Headers = new Dictionary<string, string>(),
            QueryStringParameters = new Dictionary<string, string>()
        };

        // Act
        var response = await function.FunctionHandler(request, context);

        // Assert
        Assert.True(response.Headers.ContainsKey("Access-Control-Allow-Origin"));
        Assert.Equal("*", response.Headers["Access-Control-Allow-Origin"]);
        Assert.True(response.Headers.ContainsKey("Content-Type"));
        Assert.Equal("application/json", response.Headers["Content-Type"]);
    }
}
