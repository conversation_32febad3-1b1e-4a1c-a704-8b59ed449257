using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WebScrapper.Models;
using WebScrapper.Services;

namespace WebScrapper.Controllers;
[Authorize]
[ApiController]
[Route("[controller]")]
public class PersonDetailsController: ControllerBase
{
    private readonly IWebScrapperService _webScrapperService;
    private readonly ILogger<PersonDetailsController> _logger;

    public PersonDetailsController(IWebScrapperService webScrapperService, ILogger<PersonDetailsController> logger)
    {
        _webScrapperService = webScrapperService;
        _logger = logger;
    }

    [HttpGet(Name = "GetPersonDetails")]
    public async Task<ActionResult<IEnumerable<PersonInfo>>> GetPeople(string url)
    {
        var people = await _webScrapperService.GetPersonDetailsAsync(url);

        if(people == null || people.Count == 0)
        {
            _logger.LogError("No people found for " + url);
            return NotFound("No people found");
        }
        return Ok(people);
    }
}
