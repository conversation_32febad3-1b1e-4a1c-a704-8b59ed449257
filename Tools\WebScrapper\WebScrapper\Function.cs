using Amazon.Lambda.Core;
using Amazon.Lambda.APIGatewayEvents;
using Amazon.Lambda.Serialization.SystemTextJson;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using WebScrapper.Services;
using WebScrapper.Helpers;
using WebScrapper.Models;
using Serilog;
using Serilog.Extensions.Logging;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(DefaultLambdaJsonSerializer))]

namespace WebScrapper;

public class Function
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<Function> _logger;
    private readonly IWebScrapperService _webScrapperService;

    /// <summary>
    /// Default constructor. This constructor is used by Lambda to construct the instance. When invoked in a Lambda environment
    /// the AWS credentials will come from the IAM role associated with the function and the AWS region will be set to the
    /// region the Lambda function is executed in.
    /// </summary>
    public Function()
    {
        var services = new ServiceCollection();
        ConfigureServices(services);
        _serviceProvider = services.BuildServiceProvider();
        
        _logger = _serviceProvider.GetRequiredService<ILogger<Function>>();
        _webScrapperService = _serviceProvider.GetRequiredService<IWebScrapperService>();
    }

    /// <summary>
    /// This method is called for every Lambda invocation. This method takes in an API Gateway event object and can be used 
    /// to respond to HTTP requests.
    /// </summary>
    /// <param name="request">The event for the Lambda function handler to process.</param>
    /// <param name="context">The ILambdaContext that provides methods for logging and describing the Lambda environment.</param>
    /// <returns>The response object for the Lambda function handler to return.</returns>
    public async Task<APIGatewayProxyResponse> FunctionHandler(APIGatewayProxyRequest request, ILambdaContext context)
    {
        try
        {
            _logger.LogInformation("Processing request: {Method} {Path}", request.HttpMethod, request.Path);

            // Handle different routes
            if (request.Path.StartsWith("/PersonDetails", StringComparison.OrdinalIgnoreCase) && 
                request.HttpMethod.Equals("GET", StringComparison.OrdinalIgnoreCase))
            {
                return await HandlePersonDetailsRequest(request, context);
            }
            else if (request.Path.StartsWith("/Test", StringComparison.OrdinalIgnoreCase) && 
                     request.HttpMethod.Equals("GET", StringComparison.OrdinalIgnoreCase))
            {
                return await HandleTestRequest(request, context);
            }
            else
            {
                return CreateErrorResponse(404, "Not Found", "The requested resource was not found.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception in Lambda function");
            return CreateErrorResponse(500, "Internal Server Error", "An error occurred while processing your request");
        }
    }

    private async Task<APIGatewayProxyResponse> HandlePersonDetailsRequest(APIGatewayProxyRequest request, ILambdaContext context)
    {
        try
        {
            // Check authentication
            if (!IsAuthenticated(request))
            {
                return CreateErrorResponse(401, "Unauthorized", "Authentication required");
            }

            // Get URL parameter
            string? url = null;
            if (request.QueryStringParameters?.ContainsKey("url") == true)
            {
                url = request.QueryStringParameters["url"];
            }

            if (string.IsNullOrWhiteSpace(url))
            {
                return CreateErrorResponse(400, "Bad Request", "URL parameter is required");
            }

            _logger.LogInformation("Getting person details for URL: {Url}", url);

            var people = await _webScrapperService.GetPersonDetailsAsync(url);

            if (people == null || people.Count == 0)
            {
                _logger.LogError("No people found for {Url}", url);
                return CreateErrorResponse(404, "Not Found", "No people found");
            }

            var responseBody = JsonSerializer.Serialize(people, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            return new APIGatewayProxyResponse
            {
                StatusCode = 200,
                Body = responseBody,
                Headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                    { "Access-Control-Allow-Origin", "*" },
                    { "Access-Control-Allow-Headers", "Content-Type,Authorization" },
                    { "Access-Control-Allow-Methods", "GET,POST,OPTIONS" }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing PersonDetails request");
            return CreateErrorResponse(500, "Internal Server Error", "An error occurred while processing your request");
        }
    }

    private async Task<APIGatewayProxyResponse> HandleTestRequest(APIGatewayProxyRequest request, ILambdaContext context)
    {
        var response = new { message = "API service is running" };
        var responseBody = JsonSerializer.Serialize(response);

        return await Task.FromResult(new APIGatewayProxyResponse
        {
            StatusCode = 200,
            Body = responseBody,
            Headers = new Dictionary<string, string>
            {
                { "Content-Type", "application/json" },
                { "Access-Control-Allow-Origin", "*" }
            }
        });
    }

    private bool IsAuthenticated(APIGatewayProxyRequest request)
    {
        try
        {
            if (!request.Headers.ContainsKey("Authorization"))
                return false;

            var authHeader = request.Headers["Authorization"];
            if (string.IsNullOrWhiteSpace(authHeader) || !authHeader.StartsWith("Basic "))
                return false;

            var encodedCredentials = authHeader.Substring("Basic ".Length);
            var decodedBytes = Convert.FromBase64String(encodedCredentials);
            var decodedCredentials = Encoding.UTF8.GetString(decodedBytes);

            var parts = decodedCredentials.Split(':');
            if (parts.Length != 2)
                return false;

            var username = parts[0];
            var password = parts[1];

            // TODO: Replace with environment variables or AWS Secrets Manager
            return username == "alanadmin" && password == "alan!@21ALAN";
        }
        catch
        {
            return false;
        }
    }

    private APIGatewayProxyResponse CreateErrorResponse(int statusCode, string error, string message)
    {
        var errorResponse = new
        {
            error = error,
            message = message,
            status = statusCode
        };

        var responseBody = JsonSerializer.Serialize(errorResponse);

        return new APIGatewayProxyResponse
        {
            StatusCode = statusCode,
            Body = responseBody,
            Headers = new Dictionary<string, string>
            {
                { "Content-Type", "application/json" },
                { "Access-Control-Allow-Origin", "*" }
            }
        };
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // Configuration
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        services.AddSingleton<IConfiguration>(configuration);

        // Configure Serilog for Lambda
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .CreateLogger();

        // Add logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(Log.Logger);
        });

        // Register application services
        services.AddSingleton<IWebScrapperService, WebScrapperService>();
        services.AddSingleton<IHeadLessBrowserHelper, HeadLessBrowserHelper>();
    }
}
