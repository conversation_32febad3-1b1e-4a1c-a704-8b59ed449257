﻿using System.IO.Compression;
using HtmlAgilityPack;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using WebScrapper.Models;

namespace WebScrapper.Helpers;

public class HeadLessBrowserHelper: IHeadLessBrowserHelper
{
    private readonly ILogger<HeadLessBrowserHelper> _logger;
    private readonly string basePath = AppDomain.CurrentDomain.BaseDirectory;

    public HeadLessBrowserHelper(ILogger<HeadLessBrowserHelper> logger)
    {
        _logger = logger;
    }

    public async Task<List<PersonInfo>> GetNameListFromChRegisterAsync(string uri)
    {
        _logger.LogInformation("called for the url: " + uri);
        
        if(string.IsNullOrWhiteSpace(uri))
        {
            return Enumerable.Empty<PersonInfo>().ToList();
        }

        try
        {
            var chromeDriverService = ChromeDriverService.CreateDefaultService();
            chromeDriverService.HideCommandPromptWindow = true;

            var option = new ChromeOptions
            {
                // In Docker container, use the system Chrome
                BinaryLocation = GetChromeBinaryPath(),
                LeaveBrowserRunning = false,
                PageLoadStrategy = PageLoadStrategy.Normal,
                UnhandledPromptBehavior = UnhandledPromptBehavior.Dismiss
            };

            // Add arguments for headless Chrome
            option.AddArguments(
                // commonly unwanted features
                "--disable-features=Translate",
                "--hide-scrollbars",
                "--mute-audio",
                "--no-default-browser-check",
                "--no-first-run",
                "--ash-no-nudges",
                "--disable-search-engine-choice-screen",
                // platform behavior
                "--disable-back-forward-cache",
                "--disable-features=BackForwardCache",
                "--disable-features=LazyFrameLoading",
                // Interactivity suppression
                "--autoplay-policy=user-gesture-required",
                "--deny-permission-prompts",
                "--disable-external-intent-requests",
                // general
                "--log-level=3",
                // Background networking
                "--disable-background-networking",
                "--disable-domain-reliability",
                "--disable-sync",
                "--no-pings",
                // Headless
                "--headless=new",  // Use the new headless mode
                "--incognito",
                "--lang=de-CH",
                "--proxy-bypass-list=*",
                "--proxy-server='direct://'",
                // Linux-specific flags
                "--no-sandbox",
                "--disable-dev-shm-usage"  // Overcome limited resource problems
                );

            // Initialize the Chrome Driver
            using(IWebDriver driver = new ChromeDriver(chromeDriverService, option))
            {
                // Set timeout
                driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(30);
                
                // Go to the webpage
                _logger.LogInformation("Navigating to URL: {Uri}", uri);
                driver.Navigate().GoToUrl(uri);

                // Wait for page to load
                Thread.Sleep(5000);

                // Get the page source, including any dynamically loaded content
                var pageSource = driver.PageSource;
                
                if (string.IsNullOrWhiteSpace(pageSource))
                {
                    _logger.LogError("Empty page source received from {Uri}", uri);
                    return Enumerable.Empty<PersonInfo>().ToList();
                }

                var htmlContent = pageSource
                    .Replace("\r\n", "\n") // just line ends
                    .Replace("\t", "") // no tabs
                    .Replace("&nbsp;", "") // no non blanks
                    .Replace("\\", ""); // no backslashes

                _logger.LogInformation("Page source found, length: {Length}", htmlContent.Length);
                
                // For debugging, save the HTML content
                try {
                    var debugPath = Path.Combine(basePath, "debug_page.html");
                    File.WriteAllText(debugPath, htmlContent);
                    _logger.LogInformation("Saved debug HTML to {Path}", debugPath);
                } catch (Exception ex) {
                    _logger.LogWarning("Could not save debug HTML: {Error}", ex.Message);
                }

                var people = await findPersonDetailsList(htmlContent);
                
                if (people.Count == 0)
                {
                    _logger.LogError("No people found for {Uri}", uri);
                }
                else
                {
                    _logger.LogInformation("Found {Count} people for {Uri}", people.Count, uri);
                }
                
                return people;
            }
        }
        catch(Exception ex)
        {
            _logger.LogError("Error getting person data: {Error}", ex.ToString());
            return Enumerable.Empty<PersonInfo>().ToList();
        }
    }

    private string GetChromeBinaryPath()
    {
        // Check for Chrome in common Linux locations
        string[] possiblePaths = {
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium",
            "/usr/bin/chromium-browser"
        };
        
        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
            {
                _logger.LogInformation("Using Chrome at {Path}", path);
                return path;
            }
        }
        
        // Default to the most common path
        _logger.LogWarning("Chrome not found in common locations, defaulting to /usr/bin/google-chrome");
        return "/usr/bin/google-chrome";
    }

    private async Task<List<PersonInfo>> findPersonDetailsList(string htmlContent)
    {
        var doc = new HtmlDocument();
        doc.LoadHtml(htmlContent);

        // Extract people
        List<PersonInfo> people = new();

        // Try to find the table with different selectors
        var table = doc.DocumentNode.SelectSingleNode("//table[contains(@class, 'personen')]");
        
        if(table == null)
        {
            _logger.LogWarning("No table with class 'personen' found. Trying alternative selectors...");
            
            // Try alternative selectors
            table = doc.DocumentNode.SelectSingleNode("//table[contains(@class, 'person')]");
            
            if(table == null)
            {
                _logger.LogWarning("No table with class 'person' found. Trying to find any table...");
                var allTables = doc.DocumentNode.SelectNodes("//table");
                
                if(allTables != null && allTables.Count > 0)
                {
                    _logger.LogInformation("Found {Count} tables on the page", allTables.Count);
                    
                    // Try to find a table that might contain person data
                    foreach(var t in allTables)
                    {
                        var headers = t.SelectNodes(".//th");
                        if(headers != null)
                        {
                            foreach(var header in headers)
                            {
                                var headerText = header.InnerText.ToLower();
                                if(headerText.Contains("name") || headerText.Contains("person") || 
                                   headerText.Contains("rolle") || headerText.Contains("funktion"))
                                {
                                    _logger.LogInformation("Found potential person table with header: {Header}", headerText);
                                    table = t;
                                    break;
                                }
                            }
                        }
                        
                        if(table != null) break;
                    }
                }
                else
                {
                    _logger.LogWarning("No tables found on the page");
                    return Enumerable.Empty<PersonInfo>().ToList();
                }
            }
        }
        
        if(table == null)
        {
            _logger.LogWarning("Could not find any suitable table for person data");
            return Enumerable.Empty<PersonInfo>().ToList();
        }

        // Try to find rows in different ways
        var rows = table.SelectNodes(".//tbody/tr");
        
        if(rows == null)
        {
            _logger.LogWarning("No rows found in tbody. Trying direct tr selector...");
            rows = table.SelectNodes(".//tr");
        }
        
        if(rows == null)
        {
            _logger.LogWarning("No rows found in the table");
            return Enumerable.Empty<PersonInfo>().ToList();
        }
        
        _logger.LogInformation("Found {Count} rows in the table", rows.Count);

        foreach(var row in rows)
        {
            var tds = row.SelectNodes("td");
            if(tds != null)
            {
                _logger.LogInformation("Processing row with {Count} cells", tds.Count);
                
                // Adjust the index based on the number of cells
                if(tds.Count >= 6)
                {
                    var nameTd = tds[3];
                    var classVal = nameTd.GetAttributeValue("class", "");

                    if(!classVal.Contains("strike"))
                    {
                        var person = new PersonInfo
                        {
                            PersonalDetails = nameTd.InnerText.Trim(),
                            Role = tds[4].InnerText.Trim(),
                            SigningAuthority = tds[5].InnerText.Trim()
                        };

                        _logger.LogInformation("Found person: {Details}, {Role}", person.PersonalDetails, person.Role);
                        people.Add(person);
                    }
                }
                else if(tds.Count >= 3)
                {
                    // Try with a different column structure
                    var nameTd = tds[0];
                    var person = new PersonInfo
                    {
                        PersonalDetails = nameTd.InnerText.Trim(),
                        Role = tds.Count > 1 ? tds[1].InnerText.Trim() : "",
                        SigningAuthority = tds.Count > 2 ? tds[2].InnerText.Trim() : ""
                    };

                    _logger.LogInformation("Found person (alt format): {Details}, {Role}", person.PersonalDetails, person.Role);
                    people.Add(person);
                }
            }
        }

        return await Task.FromResult(people);
    }
}
