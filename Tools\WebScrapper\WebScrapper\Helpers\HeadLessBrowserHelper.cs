using System.IO.Compression;
using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using WebScrapper.Models;

namespace WebScrapper.Helpers;

public class HeadLessBrowserHelper: IHeadLessBrowserHelper
{
    private readonly ILogger<HeadLessBrowserHelper> _logger;
    private readonly string basePath = AppDomain.CurrentDomain.BaseDirectory;

    public HeadLessBrowserHelper(ILogger<HeadLessBrowserHelper> logger)
    {
        _logger = logger;
    }

    public async Task<List<PersonInfo>> GetNameListFromChRegisterAsync(string uri)
    {
        _logger.LogInformation("called for the url: " + uri);
        
        if(string.IsNullOrWhiteSpace(uri))
        {
            return Enumerable.Empty<PersonInfo>().ToList();
        }

        try
        {
            var chromeDriverService = ChromeDriverService.CreateDefaultService();
            chromeDriverService.HideCommandPromptWindow = true;

            // Check if we're in Lambda environment
            var isLambda = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("LAMBDA_TASK_ROOT"));

            var option = new ChromeOptions
            {
                BinaryLocation = GetChromeBinaryPath(),
                LeaveBrowserRunning = false,
                PageLoadStrategy = PageLoadStrategy.Normal,
                UnhandledPromptBehavior = UnhandledPromptBehavior.Dismiss
            };

            // Add arguments for headless Chrome - optimized for Lambda
            var chromeArgs = new List<string>
            {
                // Essential headless flags
                "--headless=new",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-software-rasterizer",

                // Memory and performance optimizations for Lambda
                "--memory-pressure-off",
                "--max_old_space_size=4096",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",

                // Security and privacy
                "--incognito",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",

                // Networking
                "--disable-background-networking",
                "--disable-domain-reliability",
                "--disable-sync",
                "--no-pings",
                "--proxy-bypass-list=*",
                "--proxy-server='direct://'",

                // UI and interaction
                "--disable-features=Translate",
                "--hide-scrollbars",
                "--mute-audio",
                "--no-default-browser-check",
                "--no-first-run",
                "--disable-search-engine-choice-screen",
                "--autoplay-policy=user-gesture-required",
                "--deny-permission-prompts",
                "--disable-external-intent-requests",

                // Logging
                "--log-level=3",
                "--silent",

                // Language
                "--lang=de-CH"
            };

            // Add Lambda-specific optimizations
            if (isLambda)
            {
                chromeArgs.AddRange(new[]
                {
                    "--single-process",
                    "--disable-extensions",
                    "--disable-plugins",
                    "--disable-images",
                    "--disable-javascript",
                    "--disable-default-apps",
                    "--disable-background-mode",
                    "--disable-background-timer-throttling",
                    "--disable-client-side-phishing-detection",
                    "--disable-component-extensions-with-background-pages",
                    "--disable-ipc-flooding-protection",
                    "--disable-hang-monitor",
                    "--disable-prompt-on-repost",
                    "--disable-popup-blocking",
                    "--disable-translate",
                    "--metrics-recording-only",
                    "--no-crash-upload",
                    "--disable-logging",
                    "--disable-login-animations",
                    "--disable-notifications"
                });
            }

            option.AddArguments(chromeArgs);

            // Initialize the Chrome Driver
            using(IWebDriver driver = new ChromeDriver(chromeDriverService, option))
            {
                // Set timeout
                driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(30);
                
                // Go to the webpage
                _logger.LogInformation("Navigating to URL: {Uri}", uri);
                driver.Navigate().GoToUrl(uri);

                // Wait for page to load
                Thread.Sleep(5000);

                // Get the page source, including any dynamically loaded content
                var pageSource = driver.PageSource;
                
                if (string.IsNullOrWhiteSpace(pageSource))
                {
                    _logger.LogError("Empty page source received from {Uri}", uri);
                    return Enumerable.Empty<PersonInfo>().ToList();
                }

                var htmlContent = pageSource
                    .Replace("\r\n", "\n") // just line ends
                    .Replace("\t", "") // no tabs
                    .Replace("&nbsp;", "") // no non blanks
                    .Replace("\\", ""); // no backslashes

                _logger.LogInformation("Page source found, length: {Length}", htmlContent.Length);
                
                // For debugging, save the HTML content
                try {
                    // In Lambda, use /tmp directory for temporary files
                    var debugDir = Environment.GetEnvironmentVariable("LAMBDA_TASK_ROOT") != null ? "/tmp" : basePath;
                    var debugPath = Path.Combine(debugDir, "debug_page.html");
                    File.WriteAllText(debugPath, htmlContent);
                    _logger.LogInformation("Saved debug HTML to {Path}", debugPath);
                } catch (Exception ex) {
                    _logger.LogWarning("Could not save debug HTML: {Error}", ex.Message);
                }

                var people = await findPersonDetailsList(htmlContent);
                
                if (people.Count == 0)
                {
                    _logger.LogError("No people found for {Uri}", uri);
                }
                else
                {
                    _logger.LogInformation("Found {Count} people for {Uri}", people.Count, uri);
                }
                
                return people;
            }
        }
        catch(Exception ex)
        {
            _logger.LogError("Error getting person data: {Error}", ex.ToString());
            return Enumerable.Empty<PersonInfo>().ToList();
        }
    }

    private string GetChromeBinaryPath()
    {
        // Check if we're running in AWS Lambda
        var lambdaTaskRoot = Environment.GetEnvironmentVariable("LAMBDA_TASK_ROOT");
        if (!string.IsNullOrEmpty(lambdaTaskRoot))
        {
            // In Lambda, Chrome should be provided via a layer or bundled with the function
            var lambdaChromePath = "/opt/chrome/chrome";
            if (File.Exists(lambdaChromePath))
            {
                _logger.LogInformation("Using Lambda Chrome at {Path}", lambdaChromePath);
                return lambdaChromePath;
            }

            // Alternative Lambda Chrome paths
            var altLambdaPaths = new[]
            {
                "/opt/bin/chrome",
                "/opt/google-chrome/chrome",
                "/tmp/chrome/chrome"
            };

            foreach (var path in altLambdaPaths)
            {
                if (File.Exists(path))
                {
                    _logger.LogInformation("Using Lambda Chrome at {Path}", path);
                    return path;
                }
            }

            _logger.LogWarning("Chrome not found in Lambda environment. You may need to add a Chrome layer.");
        }

        // Check for Chrome in common Linux locations (for local development/Docker)
        string[] possiblePaths = {
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium",
            "/usr/bin/chromium-browser"
        };

        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
            {
                _logger.LogInformation("Using Chrome at {Path}", path);
                return path;
            }
        }

        // Default to the most common path
        _logger.LogWarning("Chrome not found in common locations, defaulting to /usr/bin/google-chrome");
        return "/usr/bin/google-chrome";
    }

    private async Task<List<PersonInfo>> findPersonDetailsList(string htmlContent)
    {
        var doc = new HtmlDocument();
        doc.LoadHtml(htmlContent);

        // Extract people
        List<PersonInfo> people = new();

        // Try to find the table with different selectors
        var table = doc.DocumentNode.SelectSingleNode("//table[contains(@class, 'personen')]");
        
        if(table == null)
        {
            _logger.LogWarning("No table with class 'personen' found. Trying alternative selectors...");
            
            // Try alternative selectors
            table = doc.DocumentNode.SelectSingleNode("//table[contains(@class, 'person')]");
            
            if(table == null)
            {
                _logger.LogWarning("No table with class 'person' found. Trying to find any table...");
                var allTables = doc.DocumentNode.SelectNodes("//table");
                
                if(allTables != null && allTables.Count > 0)
                {
                    _logger.LogInformation("Found {Count} tables on the page", allTables.Count);
                    
                    // Try to find a table that might contain person data
                    foreach(var t in allTables)
                    {
                        var headers = t.SelectNodes(".//th");
                        if(headers != null)
                        {
                            foreach(var header in headers)
                            {
                                var headerText = header.InnerText.ToLower();
                                if(headerText.Contains("name") || headerText.Contains("person") || 
                                   headerText.Contains("rolle") || headerText.Contains("funktion"))
                                {
                                    _logger.LogInformation("Found potential person table with header: {Header}", headerText);
                                    table = t;
                                    break;
                                }
                            }
                        }
                        
                        if(table != null) break;
                    }
                }
                else
                {
                    _logger.LogWarning("No tables found on the page");
                    return Enumerable.Empty<PersonInfo>().ToList();
                }
            }
        }
        
        if(table == null)
        {
            _logger.LogWarning("Could not find any suitable table for person data");
            return Enumerable.Empty<PersonInfo>().ToList();
        }

        // Try to find rows in different ways
        var rows = table.SelectNodes(".//tbody/tr");
        
        if(rows == null)
        {
            _logger.LogWarning("No rows found in tbody. Trying direct tr selector...");
            rows = table.SelectNodes(".//tr");
        }
        
        if(rows == null)
        {
            _logger.LogWarning("No rows found in the table");
            return Enumerable.Empty<PersonInfo>().ToList();
        }
        
        _logger.LogInformation("Found {Count} rows in the table", rows.Count);

        foreach(var row in rows)
        {
            var tds = row.SelectNodes("td");
            if(tds != null)
            {
                _logger.LogInformation("Processing row with {Count} cells", tds.Count);
                
                // Adjust the index based on the number of cells
                if(tds.Count >= 6)
                {
                    var nameTd = tds[3];
                    var classVal = nameTd.GetAttributeValue("class", "");

                    if(!classVal.Contains("strike"))
                    {
                        var person = new PersonInfo
                        {
                            PersonalDetails = nameTd.InnerText.Trim(),
                            Role = tds[4].InnerText.Trim(),
                            SigningAuthority = tds[5].InnerText.Trim()
                        };

                        _logger.LogInformation("Found person: {Details}, {Role}", person.PersonalDetails, person.Role);
                        people.Add(person);
                    }
                }
                else if(tds.Count >= 3)
                {
                    // Try with a different column structure
                    var nameTd = tds[0];
                    var person = new PersonInfo
                    {
                        PersonalDetails = nameTd.InnerText.Trim(),
                        Role = tds.Count > 1 ? tds[1].InnerText.Trim() : "",
                        SigningAuthority = tds.Count > 2 ? tds[2].InnerText.Trim() : ""
                    };

                    _logger.LogInformation("Found person (alt format): {Details}, {Role}", person.PersonalDetails, person.Role);
                    people.Add(person);
                }
            }
        }

        return await Task.FromResult(people);
    }
}
