# WebScrapper Lambda Deployment Script (PowerShell)

param(
    [string]$BasicAuthUsername = "alanadmin",
    [string]$BasicAuthPassword = "alan!@21ALAN",
    [switch]$UseSAM = $false
)

Write-Host "🚀 Starting WebScrapper Lambda deployment..." -ForegroundColor Green

# Check if AWS CLI is installed
try {
    aws --version | Out-Null
    Write-Host "✅ AWS CLI found" -ForegroundColor Green
} catch {
    Write-Host "❌ AWS CLI is not installed. Please install it first." -ForegroundColor Red
    exit 1
}

# Check if .NET 8 is installed
try {
    dotnet --version | Out-Null
    Write-Host "✅ .NET SDK found" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET 8 SDK is not installed. Please install it first." -ForegroundColor Red
    exit 1
}

# Check if SAM CLI is installed (optional)
try {
    sam --version | Out-Null
    Write-Host "✅ SAM CLI found. Using SAM for deployment." -ForegroundColor Green
    $UseSAM = $true
} catch {
    Write-Host "⚠️  SAM CLI not found. Using Serverless Framework." -ForegroundColor Yellow
    $UseSAM = $false
}

# Set environment variables
$env:BASIC_AUTH_USERNAME = $BasicAuthUsername
$env:BASIC_AUTH_PASSWORD = $BasicAuthPassword

Write-Host "📦 Building .NET application..." -ForegroundColor Blue
Set-Location WebScrapper

# Clean and restore
dotnet clean
dotnet restore

# Build in Release mode
dotnet build -c Release

# Publish the application
dotnet publish -c Release -o bin/Release/net8.0/publish

# Create deployment package
Set-Location bin/Release/net8.0
Compress-Archive -Path publish/* -DestinationPath WebScrapper.zip -Force

Set-Location ../../../..

if ($UseSAM) {
    Write-Host "🔨 Deploying with AWS SAM..." -ForegroundColor Blue
    
    # Build with SAM
    sam build
    
    # Deploy with SAM
    sam deploy --guided --parameter-overrides BasicAuthUsername="$BasicAuthUsername" BasicAuthPassword="$BasicAuthPassword"
        
} else {
    Write-Host "🔨 Deploying with Serverless Framework..." -ForegroundColor Blue
    
    # Check if Serverless is installed
    try {
        serverless --version | Out-Null
        Write-Host "✅ Serverless Framework found" -ForegroundColor Green
    } catch {
        try {
            sls --version | Out-Null
            Write-Host "✅ Serverless Framework found" -ForegroundColor Green
        } catch {
            Write-Host "❌ Serverless Framework is not installed. Installing..." -ForegroundColor Yellow
            npm install -g serverless
        }
    }
    
    # Deploy with Serverless
    serverless deploy --verbose
}

Write-Host "✅ Deployment completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Note the API Gateway URL from the deployment output"
Write-Host "2. Test the /Test endpoint to verify the deployment"
Write-Host "3. Test the /PersonDetails endpoint with a valid Swiss company register URL"
Write-Host ""
Write-Host "🔗 Example test commands:" -ForegroundColor Cyan
Write-Host "curl -X GET 'https://YOUR_API_GATEWAY_URL/Test'"
Write-Host "curl -X GET 'https://YOUR_API_GATEWAY_URL/PersonDetails?url=https://zh.chregister.ch/cr-portal/auszug/auszug.xhtml?uid=CHE-442.398.437' -H 'Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg=='"
Write-Host ""
Write-Host "⚠️  Important: Make sure to add a Chrome layer to your Lambda function for web scraping to work!" -ForegroundColor Yellow
Write-Host "   You can use a public layer like: arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31"
