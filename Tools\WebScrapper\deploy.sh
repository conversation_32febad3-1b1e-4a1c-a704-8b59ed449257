#!/bin/bash

# WebScrapper Lambda Deployment Script

set -e

echo "🚀 Starting WebScrapper Lambda deployment..."

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if .NET 8 is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET 8 SDK is not installed. Please install it first."
    exit 1
fi

# Check if SAM CLI is installed (optional)
if command -v sam &> /dev/null; then
    echo "✅ SAM CLI found. Using SAM for deployment."
    USE_SAM=true
else
    echo "⚠️  SAM CLI not found. Using Serverless Framework."
    USE_SAM=false
fi

# Set environment variables if not set
export BASIC_AUTH_USERNAME=${BASIC_AUTH_USERNAME:-"alanadmin"}
export BASIC_AUTH_PASSWORD=${BASIC_AUTH_PASSWORD:-"alan!@21ALAN"}

echo "📦 Building .NET application..."
cd WebScrapper

# Clean and restore
dotnet clean
dotnet restore

# Build in Release mode
dotnet build -c Release

# Publish the application
dotnet publish -c Release -o bin/Release/net8.0/publish

# Create deployment package
cd bin/Release/net8.0
zip -r WebScrapper.zip publish/

cd ../../../../

if [ "$USE_SAM" = true ]; then
    echo "🔨 Deploying with AWS SAM..."
    
    # Build with SAM
    sam build
    
    # Deploy with SAM
    sam deploy --guided --parameter-overrides \
        BasicAuthUsername="$BASIC_AUTH_USERNAME" \
        BasicAuthPassword="$BASIC_AUTH_PASSWORD"
        
else
    echo "🔨 Deploying with Serverless Framework..."
    
    # Check if Serverless is installed
    if ! command -v serverless &> /dev/null && ! command -v sls &> /dev/null; then
        echo "❌ Serverless Framework is not installed. Installing..."
        npm install -g serverless
    fi
    
    # Deploy with Serverless
    serverless deploy --verbose
fi

echo "✅ Deployment completed!"
echo ""
echo "📋 Next steps:"
echo "1. Note the API Gateway URL from the deployment output"
echo "2. Test the /Test endpoint to verify the deployment"
echo "3. Test the /PersonDetails endpoint with a valid Swiss company register URL"
echo ""
echo "🔗 Example test commands:"
echo "curl -X GET 'https://YOUR_API_GATEWAY_URL/Test'"
echo "curl -X GET 'https://YOUR_API_GATEWAY_URL/PersonDetails?url=https://zh.chregister.ch/cr-portal/auszug/auszug.xhtml?uid=CHE-442.398.437' -H 'Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg=='"
echo ""
echo "⚠️  Important: Make sure to add a Chrome layer to your Lambda function for web scraping to work!"
echo "   You can use a public layer like: arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31"
