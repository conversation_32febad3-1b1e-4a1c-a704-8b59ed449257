service: webscrapper

frameworkVersion: '3'

provider:
  name: aws
  runtime: dotnet8
  region: us-east-1
  stage: ${opt:stage, 'dev'}
  memorySize: 2048
  timeout: 300
  architecture: x86_64
  
  environment:
    ASPNETCORE_ENVIRONMENT: Production
    BASIC_AUTH_USERNAME: ${env:BASIC_AUTH_USERNAME, 'alanadmin'}
    BASIC_AUTH_PASSWORD: ${env:BASIC_AUTH_PASSWORD, 'alan!@21ALAN'}
  
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: 
            - 'arn:aws:logs:${aws:region}:${aws:accountId}:log-group:/aws/lambda/*:*:*'

package:
  artifact: WebScrapper/bin/Release/net8.0/WebScrapper.zip

functions:
  webscrapper:
    handler: WebScrapper::WebScrapper.Function::FunctionHandler
    package:
      artifact: WebScrapper/bin/Release/net8.0/WebScrapper.zip
    layers:
      # Chrome Layer - you can use a public layer or create your own
      # Example public layer: arn:aws:lambda:us-east-1:************:layer:chrome-aws-lambda:31
      - arn:aws:lambda:${aws:region}:************:layer:chrome-aws-lambda:31
    events:
      - http:
          path: /PersonDetails
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - Authorization
            allowCredentials: false
      - http:
          path: /Test
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - Authorization
            allowCredentials: false

plugins:
  - serverless-dotnet

custom:
  dotnetCliPath: dotnet

resources:
  Resources:
    # API Gateway configuration
    GatewayResponseDefault4XX:
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        ResponseType: DEFAULT_4XX
        RestApiId:
          Ref: 'RestApiApigEvent'
    GatewayResponseDefault5XX:
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        ResponseType: DEFAULT_5XX
        RestApiId:
          Ref: 'RestApiApigEvent'

  Outputs:
    WebScrapperApiUrl:
      Description: URL of the WebScrapper API
      Value:
        Fn::Join:
          - ''
          - - 'https://'
            - Ref: RestApiApigEvent
            - '.execute-api.'
            - ${aws:region}
            - '.amazonaws.com/'
            - ${self:provider.stage}
      Export:
        Name: ${self:service}-${self:provider.stage}-api-url
