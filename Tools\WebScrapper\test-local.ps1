# Local Testing Script for WebScrapper Lambda Function

param(
    [string]$TestUrl = "https://zh.chregister.ch/cr-portal/auszug/auszug.xhtml?uid=CHE-442.398.437",
    [switch]$RunUnitTests = $false,
    [switch]$StartLocalApi = $false
)

Write-Host "🧪 WebScrapper Lambda Local Testing" -ForegroundColor Green

if ($RunUnitTests) {
    Write-Host "🔬 Running unit tests..." -ForegroundColor Blue
    
    Set-Location WebScrapper.Tests
    dotnet test --verbosity normal
    Set-Location ..
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Unit tests failed!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Unit tests passed!" -ForegroundColor Green
}

if ($StartLocalApi) {
    Write-Host "🚀 Starting local API with SAM..." -ForegroundColor Blue
    
    # Check if SAM is available
    try {
        sam --version | Out-Null
    } catch {
        Write-Host "❌ SAM CLI not found. Please install SAM CLI first." -ForegroundColor Red
        exit 1
    }
    
    # Build and start local API
    sam build
    
    Write-Host "🌐 Starting local API on http://localhost:3000" -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
    
    sam local start-api --port 3000
} else {
    Write-Host "🔧 Testing Lambda function locally..." -ForegroundColor Blue
    
    # Build the project
    Set-Location WebScrapper
    dotnet build -c Release
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Build successful!" -ForegroundColor Green
    
    # Create test event files
    $testEvent = @{
        httpMethod = "GET"
        path = "/Test"
        headers = @{}
        queryStringParameters = @{}
    } | ConvertTo-Json -Depth 3
    
    $testEvent | Out-File -FilePath "test-event.json" -Encoding UTF8
    
    $personDetailsEvent = @{
        httpMethod = "GET"
        path = "/PersonDetails"
        headers = @{
            Authorization = "Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg=="
        }
        queryStringParameters = @{
            url = $TestUrl
        }
    } | ConvertTo-Json -Depth 3
    
    $personDetailsEvent | Out-File -FilePath "person-details-event.json" -Encoding UTF8
    
    Write-Host "📝 Created test event files:" -ForegroundColor Cyan
    Write-Host "  - test-event.json (for /Test endpoint)"
    Write-Host "  - person-details-event.json (for /PersonDetails endpoint)"
    
    # Test with Lambda Test Tool if available
    try {
        dotnet lambda-test-tool-8.0 --help | Out-Null
        Write-Host "🛠️  Lambda Test Tool found. You can run:" -ForegroundColor Green
        Write-Host "  dotnet lambda-test-tool-8.0" -ForegroundColor Yellow
    } catch {
        Write-Host "💡 To install Lambda Test Tool, run:" -ForegroundColor Cyan
        Write-Host "  dotnet tool install -g Amazon.Lambda.TestTool-8.0" -ForegroundColor Yellow
    }
    
    Set-Location ..
}

Write-Host ""
Write-Host "📋 Available testing options:" -ForegroundColor Cyan
Write-Host "1. Run unit tests: .\test-local.ps1 -RunUnitTests"
Write-Host "2. Start local API: .\test-local.ps1 -StartLocalApi"
Write-Host "3. Use Lambda Test Tool: cd WebScrapper && dotnet lambda-test-tool-8.0"
Write-Host ""
Write-Host "🔗 Test commands for local API (when running):" -ForegroundColor Cyan
Write-Host "curl http://localhost:3000/Test"
Write-Host "curl 'http://localhost:3000/PersonDetails?url=$TestUrl' -H 'Authorization: Basic YWxhbmFkbWluOmFsYW4hQDIxQUxBTg=='"
