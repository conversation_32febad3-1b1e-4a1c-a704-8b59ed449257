# Verify Lambda Function Structure

Write-Host "Verifying Lambda Function Setup" -ForegroundColor Green

# Build the project
Write-Host "Building project..." -ForegroundColor Blue
Set-Location WebScrapper
dotnet build -c Release

if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Build successful!" -ForegroundColor Green

# Check if Function.cs exists and has the right structure
if (Test-Path "Function.cs") {
    $functionContent = Get-Content "Function.cs" -Raw
    
    if ($functionContent -match "class Function" -and 
        $functionContent -match "FunctionHandler" -and 
        $functionContent -match "APIGatewayProxyRequest" -and
        $functionContent -match "APIGatewayProxyResponse") {
        Write-Host "SUCCESS: Function.cs has correct Lambda structure!" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Function.cs may be missing required Lambda components" -ForegroundColor Yellow
    }
} else {
    Write-Host "ERROR: Function.cs not found!" -ForegroundColor Red
}

# Check if all required packages are referenced
$projectContent = Get-Content "WebScrapper.csproj" -Raw
$requiredPackages = @("Amazon.Lambda.Core", "Amazon.Lambda.APIGatewayEvents", "Amazon.Lambda.Serialization.SystemTextJson")

foreach ($package in $requiredPackages) {
    if ($projectContent -match $package) {
        Write-Host "Package $package is referenced" -ForegroundColor Green
    } else {
        Write-Host "Package $package is missing" -ForegroundColor Red
    }
}

Set-Location ..

Write-Host ""
Write-Host "Verification completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Your Lambda function is ready for deployment!" -ForegroundColor Cyan
